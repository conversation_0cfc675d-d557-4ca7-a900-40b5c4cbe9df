import re
from pyrogram.types import Message

# استخراج معرف المستخدم من الرسالة
async def extract_user(message: Message):
    """استخراج معرف المستخدم من الرسالة"""
    user_id = None
    
    # إذا كانت الرسالة رد على رسالة أخرى
    if message.reply_to_message and message.reply_to_message.from_user:
        return message.reply_to_message.from_user.id
    
    # إذا كان هناك نص في الرسالة
    if len(message.command) > 1:
        text = message.command[1]
        
        # إذا كان النص معرف مستخدم
        if text.startswith("@"):
            username = text[1:]
            return username  # سيتم البحث عن المستخدم بواسطة المعرف لاحقًا
        
        # إذا كان النص معرف رقمي
        if text.isdigit():
            return int(text)
    
    # إذا كان المستخدم مذكورًا في الرسالة
    if message.entities:
        for entity in message.entities:
            if entity.type == "mention":
                return message.text[entity.offset:entity.offset + entity.length][1:]
            
            if entity.type == "text_mention":
                return entity.user.id
    
    # إذا لم يتم العثور على معرف المستخدم
    return message.from_user.id

# استخراج معرف المجموعة من الرسالة
async def extract_group(message: Message):
    """استخراج معرف المجموعة من الرسالة"""
    # إذا كان هناك نص في الرسالة
    if len(message.command) > 1:
        text = message.command[1]
        
        # إذا كان النص معرف مجموعة
        if text.startswith("@"):
            return text[1:]  # سيتم البحث عن المجموعة بواسطة المعرف لاحقًا
        
        # إذا كان النص معرف رقمي أو رابط
        if text.isdigit() or text.startswith("-100"):
            return int(text.replace("-100", ""))
        
        # إذا كان النص رابط مجموعة
        if "t.me/" in text:
            match = re.search(r"t\.me/(?:c/)?([^/]+)", text)
            if match:
                return match.group(1)
    
    # إذا لم يتم العثور على معرف المجموعة
    return message.chat.id if message.chat.type in ["group", "supergroup"] else None

# استخراج رابط من الرسالة
async def extract_link(message: Message):
    """استخراج رابط من الرسالة"""
    # إذا كان هناك نص في الرسالة
    if len(message.command) > 1:
        text = " ".join(message.command[1:])
        
        # البحث عن روابط في النص
        url_pattern = re.compile(r'https?://\S+')
        match = url_pattern.search(text)
        if match:
            return match.group(0)
    
    # إذا كانت الرسالة رد على رسالة أخرى
    if message.reply_to_message and message.reply_to_message.text:
        text = message.reply_to_message.text
        
        # البحث عن روابط في النص
        url_pattern = re.compile(r'https?://\S+')
        match = url_pattern.search(text)
        if match:
            return match.group(0)
    
    # إذا لم يتم العثور على رابط
    return None

# استخراج نص من الرسالة
async def extract_text(message: Message):
    """استخراج نص من الرسالة"""
    # إذا كان هناك نص في الرسالة
    if len(message.command) > 1:
        return " ".join(message.command[1:])
    
    # إذا كانت الرسالة رد على رسالة أخرى
    if message.reply_to_message:
        if message.reply_to_message.text:
            return message.reply_to_message.text
        elif message.reply_to_message.caption:
            return message.reply_to_message.caption
    
    # إذا لم يتم العثور على نص
    return ""

# استخراج اسم الأغنية من الرسالة
async def extract_song_name(message: Message):
    """استخراج اسم الأغنية من الرسالة"""
    # إذا كان هناك نص في الرسالة
    if len(message.command) > 1:
        return " ".join(message.command[1:])
    
    # إذا كانت الرسالة رد على رسالة أخرى
    if message.reply_to_message:
        if message.reply_to_message.text:
            return message.reply_to_message.text
        elif message.reply_to_message.caption:
            return message.reply_to_message.caption
    
    # إذا لم يتم العثور على اسم الأغنية
    return None
