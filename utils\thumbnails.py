import os
import aiohttp
import aiofiles
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from database.settings_db import get_bot_name

# الألوان
BACKGROUND_COLOR = (0, 0, 0, 180)  # أسود شفاف
TEXT_COLOR = (255, 255, 255)  # أبيض
SHADOW_COLOR = (0, 0, 0)  # أسود

# المسارات
TEMP_DIR = "temp"
FONT_PATH = os.path.join(TEMP_DIR, "font.ttf")
DEFAULT_THUMB = os.path.join(TEMP_DIR, "default.jpg")

# التأكد من وجود المجلد المؤقت
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

# تنزيل الخط إذا لم يكن موجودًا
async def download_font():
    """تنزيل الخط إذا لم يكن موجودًا"""
    if not os.path.exists(FONT_PATH):
        font_url = "https://github.com/google/fonts/raw/main/ofl/tajawal/Tajawal-Bold.ttf"
        async with aiohttp.ClientSession() as session:
            async with session.get(font_url) as response:
                if response.status == 200:
                    async with aiofiles.open(FONT_PATH, "wb") as f:
                        await f.write(await response.read())

# تنزيل صورة افتراضية إذا لم تكن موجودة
async def download_default_thumb():
    """تنزيل صورة افتراضية إذا لم تكن موجودة"""
    if not os.path.exists(DEFAULT_THUMB):
        thumb_url = "https://i.imgur.com/iEpAaP1.jpg"
        async with aiohttp.ClientSession() as session:
            async with session.get(thumb_url) as response:
                if response.status == 200:
                    async with aiofiles.open(DEFAULT_THUMB, "wb") as f:
                        await f.write(await response.read())

# إنشاء صورة مصغرة للأغنية
async def generate_thumb(title, artist=None, thumbnail_url=None):
    """إنشاء صورة مصغرة للأغنية"""
    # تنزيل الخط والصورة الافتراضية إذا لم تكن موجودة
    await download_font()
    await download_default_thumb()
    
    # تحديد مسار الصورة المصغرة
    thumb_path = os.path.join(TEMP_DIR, f"thumb_{abs(hash(title))}.jpg")
    
    # تنزيل الصورة المصغرة إذا كانت متوفرة
    if thumbnail_url:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(thumbnail_url) as response:
                    if response.status == 200:
                        async with aiofiles.open(thumb_path, "wb") as f:
                            await f.write(await response.read())
        except Exception:
            # استخدام الصورة الافتراضية في حالة الفشل
            thumb_path = DEFAULT_THUMB
    else:
        # استخدام الصورة الافتراضية إذا لم تكن هناك صورة مصغرة
        thumb_path = DEFAULT_THUMB
    
    # فتح الصورة
    img = Image.open(thumb_path)
    
    # تغيير حجم الصورة إلى 1280x720
    img = img.resize((1280, 720))
    
    # إنشاء طبقة شفافة
    overlay = Image.new("RGBA", img.size, BACKGROUND_COLOR)
    
    # دمج الطبقة الشفافة مع الصورة
    img = Image.alpha_composite(img.convert("RGBA"), overlay)
    
    # إنشاء كائن رسم
    draw = ImageDraw.Draw(img)
    
    # تحميل الخط
    title_font = ImageFont.truetype(FONT_PATH, 60)
    artist_font = ImageFont.truetype(FONT_PATH, 40)
    bot_font = ImageFont.truetype(FONT_PATH, 30)
    
    # الحصول على اسم البوت
    bot_name = await get_bot_name()
    
    # تقصير العنوان إذا كان طويلاً
    if len(title) > 30:
        title = title[:27] + "..."
    
    # تقصير اسم الفنان إذا كان طويلاً
    if artist and len(artist) > 30:
        artist = artist[:27] + "..."
    
    # حساب موضع النص
    title_width, title_height = draw.textsize(title, font=title_font)
    title_position = ((img.width - title_width) // 2, img.height - 200)
    
    # رسم ظل للعنوان
    draw.text((title_position[0] + 2, title_position[1] + 2), title, font=title_font, fill=SHADOW_COLOR)
    
    # رسم العنوان
    draw.text(title_position, title, font=title_font, fill=TEXT_COLOR)
    
    # رسم اسم الفنان إذا كان متوفرًا
    if artist:
        artist_width, artist_height = draw.textsize(artist, font=artist_font)
        artist_position = ((img.width - artist_width) // 2, img.height - 120)
        
        # رسم ظل لاسم الفنان
        draw.text((artist_position[0] + 2, artist_position[1] + 2), artist, font=artist_font, fill=SHADOW_COLOR)
        
        # رسم اسم الفنان
        draw.text(artist_position, artist, font=artist_font, fill=TEXT_COLOR)
    
    # رسم اسم البوت
    bot_text = f"🎵 {bot_name}"
    bot_width, bot_height = draw.textsize(bot_text, font=bot_font)
    bot_position = ((img.width - bot_width) // 2, img.height - 50)
    
    # رسم ظل لاسم البوت
    draw.text((bot_position[0] + 2, bot_position[1] + 2), bot_text, font=bot_font, fill=SHADOW_COLOR)
    
    # رسم اسم البوت
    draw.text(bot_position, bot_text, font=bot_font, fill=TEXT_COLOR)
    
    # حفظ الصورة
    output_path = os.path.join(TEMP_DIR, f"output_{abs(hash(title))}.jpg")
    img = img.convert("RGB")
    img.save(output_path, "JPEG")
    
    return output_path
