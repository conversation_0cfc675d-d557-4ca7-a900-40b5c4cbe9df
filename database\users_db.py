import logging
from database.redis_db import (
    redis_sadd, redis_srem, redis_smembers, redis_sismember,
    redis_hset, redis_hget, redis_hdel, redis_hgetall, redis_exists
)

# إعداد التسجيل
LOGGER = logging.getLogger(__name__)

# مفاتيح Redis
USERS_SET = "users"
GROUPS_SET = "groups"
USER_DATA_PREFIX = "user:"
GROUP_DATA_PREFIX = "group:"
BANNED_USERS_SET = "banned_users"
BANNED_GROUPS_SET = "banned_groups"
DEVELOPERS_SET = "developers"
ADMINS_SET = "admins"
VIP_USERS_SET = "vip_users"

# وظائف إدارة المستخدمين
async def add_user(user_id, username=None, first_name=None):
    """إضافة مستخدم جديد إلى قاعدة البيانات"""
    await redis_sadd(USERS_SET, user_id)
    
    # تخزين بيانات المستخدم
    if username or first_name:
        user_data = {}
        if username:
            user_data["username"] = username
        if first_name:
            user_data["first_name"] = first_name
        
        for key, value in user_data.items():
            await redis_hset(f"{USER_DATA_PREFIX}{user_id}", key, value)
    
    LOGGER.info(f"تمت إضافة المستخدم: {user_id}")
    return True

async def get_user_data(user_id):
    """الحصول على بيانات المستخدم"""
    return await redis_hgetall(f"{USER_DATA_PREFIX}{user_id}")

async def update_user_data(user_id, key, value):
    """تحديث بيانات المستخدم"""
    await redis_hset(f"{USER_DATA_PREFIX}{user_id}", key, value)
    return True

async def get_all_users():
    """الحصول على جميع المستخدمين"""
    return await redis_smembers(USERS_SET)

async def count_users():
    """عدد المستخدمين"""
    users = await get_all_users()
    return len(users)

# وظائف إدارة المجموعات
async def add_group(group_id, title=None, username=None, added_by=None):
    """إضافة مجموعة جديدة إلى قاعدة البيانات"""
    await redis_sadd(GROUPS_SET, group_id)
    
    # تخزين بيانات المجموعة
    group_data = {}
    if title:
        group_data["title"] = title
    if username:
        group_data["username"] = username
    if added_by:
        group_data["added_by"] = added_by
    
    for key, value in group_data.items():
        await redis_hset(f"{GROUP_DATA_PREFIX}{group_id}", key, value)
    
    LOGGER.info(f"تمت إضافة المجموعة: {group_id}")
    return True

async def get_group_data(group_id):
    """الحصول على بيانات المجموعة"""
    return await redis_hgetall(f"{GROUP_DATA_PREFIX}{group_id}")

async def update_group_data(group_id, key, value):
    """تحديث بيانات المجموعة"""
    await redis_hset(f"{GROUP_DATA_PREFIX}{group_id}", key, value)
    return True

async def get_all_groups():
    """الحصول على جميع المجموعات"""
    return await redis_smembers(GROUPS_SET)

async def count_groups():
    """عدد المجموعات"""
    groups = await get_all_groups()
    return len(groups)

# وظائف إدارة الرتب
async def add_developer(user_id):
    """إضافة مطور"""
    await redis_sadd(DEVELOPERS_SET, user_id)
    return True

async def remove_developer(user_id):
    """إزالة مطور"""
    await redis_srem(DEVELOPERS_SET, user_id)
    return True

async def is_developer(user_id):
    """التحقق مما إذا كان المستخدم مطورًا"""
    return await redis_sismember(DEVELOPERS_SET, user_id)

async def get_all_developers():
    """الحصول على جميع المطورين"""
    return await redis_smembers(DEVELOPERS_SET)

async def add_admin(user_id):
    """إضافة مشرف"""
    await redis_sadd(ADMINS_SET, user_id)
    return True

async def remove_admin(user_id):
    """إزالة مشرف"""
    await redis_srem(ADMINS_SET, user_id)
    return True

async def is_admin(user_id):
    """التحقق مما إذا كان المستخدم مشرفًا"""
    return await redis_sismember(ADMINS_SET, user_id)

async def get_all_admins():
    """الحصول على جميع المشرفين"""
    return await redis_smembers(ADMINS_SET)

async def add_vip(user_id):
    """إضافة مستخدم VIP"""
    await redis_sadd(VIP_USERS_SET, user_id)
    return True

async def remove_vip(user_id):
    """إزالة مستخدم VIP"""
    await redis_srem(VIP_USERS_SET, user_id)
    return True

async def is_vip(user_id):
    """التحقق مما إذا كان المستخدم VIP"""
    return await redis_sismember(VIP_USERS_SET, user_id)

async def get_all_vips():
    """الحصول على جميع مستخدمي VIP"""
    return await redis_smembers(VIP_USERS_SET)

# وظائف الحظر
async def ban_user(user_id):
    """حظر مستخدم"""
    await redis_sadd(BANNED_USERS_SET, user_id)
    return True

async def unban_user(user_id):
    """إلغاء حظر مستخدم"""
    await redis_srem(BANNED_USERS_SET, user_id)
    return True

async def is_user_banned(user_id):
    """التحقق مما إذا كان المستخدم محظورًا"""
    return await redis_sismember(BANNED_USERS_SET, user_id)

async def get_all_banned_users():
    """الحصول على جميع المستخدمين المحظورين"""
    return await redis_smembers(BANNED_USERS_SET)

async def ban_group(group_id):
    """حظر مجموعة"""
    await redis_sadd(BANNED_GROUPS_SET, group_id)
    return True

async def unban_group(group_id):
    """إلغاء حظر مجموعة"""
    await redis_srem(BANNED_GROUPS_SET, group_id)
    return True

async def is_group_banned(group_id):
    """التحقق مما إذا كانت المجموعة محظورة"""
    return await redis_sismember(BANNED_GROUPS_SET, group_id)

async def get_all_banned_groups():
    """الحصول على جميع المجموعات المحظورة"""
    return await redis_smembers(BANNED_GROUPS_SET)
