import asyncio
from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import get_all_users, get_all_groups
from utils.logger import log_broadcast

# معالجة أمر الإذاعة العامة
@Client.on_message(command_handler(["broadcast", "اذاعة"]) & dev)
@dev_only
async def broadcast_command(client: Client, message: Message):
    """معالجة أمر الإذاعة العامة"""
    # التحقق من وجود رسالة للإذاعة
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "مثال: `اذاعة مرحبًا بالجميع!`"
        )
        return

    # الحصول على رسالة الإذاعة
    if message.reply_to_message:
        broadcast_message = message.reply_to_message
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    # الحصول على قائمة المستخدمين والمجموعات
    users = await get_all_users()
    groups = await get_all_groups()
    all_chats = users + groups

    # إرسال رسالة البدء
    progress_message = await message.reply_text(
        f"🔄 **جاري إرسال الإذاعة إلى {len(all_chats)} دردشة...**"
    )

    # متغيرات لتتبع التقدم
    success = 0
    failed = 0

    # إرسال الإذاعة
    for chat_id in all_chats:
        try:
            if message.reply_to_message:
                await broadcast_message.copy(chat_id)
            else:
                await client.send_message(
                    chat_id=chat_id,
                    text=broadcast_text
                )
            success += 1
        except Exception:
            failed += 1

        # تحديث رسالة التقدم كل 25 دردشة
        if (success + failed) % 25 == 0:
            await progress_message.edit_text(
                f"🔄 **جاري إرسال الإذاعة...**\n\n"
                f"✅ **تم:** {success}\n"
                f"❌ **فشل:** {failed}\n"
                f"⏳ **المتبقي:** {len(all_chats) - (success + failed)}"
            )

        # تأخير لتجنب التقييد
        await asyncio.sleep(0.1)

    # تسجيل الإذاعة
    await log_broadcast(
        client,
        "عامة",
        message.from_user.id,
        success,
        failed
    )

    # إرسال رسالة الانتهاء
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة بنجاح!**\n\n"
        f"✅ **تم:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"👥 **إجمالي:** {len(all_chats)}"
    )

# معالجة أمر الإذاعة للمستخدمين
@Client.on_message(command_handler(["broadcastusers", "اذاعة للمستخدمين"]) & dev)
@dev_only
async def broadcast_users_command(client: Client, message: Message):
    """معالجة أمر الإذاعة للمستخدمين"""
    # التحقق من وجود رسالة للإذاعة
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "مثال: `اذاعة للمستخدمين مرحبًا بالجميع!`"
        )
        return

    # الحصول على رسالة الإذاعة
    if message.reply_to_message:
        broadcast_message = message.reply_to_message
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    # الحصول على قائمة المستخدمين
    users = await get_all_users()

    # إرسال رسالة البدء
    progress_message = await message.reply_text(
        f"🔄 **جاري إرسال الإذاعة إلى {len(users)} مستخدم...**"
    )

    # متغيرات لتتبع التقدم
    success = 0
    failed = 0

    # إرسال الإذاعة
    for user_id in users:
        try:
            if message.reply_to_message:
                await broadcast_message.copy(user_id)
            else:
                await client.send_message(
                    chat_id=user_id,
                    text=broadcast_text
                )
            success += 1
        except Exception:
            failed += 1

        # تحديث رسالة التقدم كل 25 مستخدم
        if (success + failed) % 25 == 0:
            await progress_message.edit_text(
                f"🔄 **جاري إرسال الإذاعة للمستخدمين...**\n\n"
                f"✅ **تم:** {success}\n"
                f"❌ **فشل:** {failed}\n"
                f"⏳ **المتبقي:** {len(users) - (success + failed)}"
            )

        # تأخير لتجنب التقييد
        await asyncio.sleep(0.1)

    # تسجيل الإذاعة
    await log_broadcast(
        client,
        "للمستخدمين",
        message.from_user.id,
        success,
        failed
    )

    # إرسال رسالة الانتهاء
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة للمستخدمين بنجاح!**\n\n"
        f"✅ **تم:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"👥 **إجمالي:** {len(users)}"
    )

# معالجة أمر الإذاعة للمجموعات
@Client.on_message(command_handler(["broadcastgroups", "اذاعة للمجموعات"]) & dev)
@dev_only
async def broadcast_groups_command(client: Client, message: Message):
    """معالجة أمر الإذاعة للمجموعات"""
    # التحقق من وجود رسالة للإذاعة
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "مثال: `اذاعة للمجموعات مرحبًا بالجميع!`"
        )
        return

    # الحصول على رسالة الإذاعة
    if message.reply_to_message:
        broadcast_message = message.reply_to_message
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    # الحصول على قائمة المجموعات
    groups = await get_all_groups()

    # إرسال رسالة البدء
    progress_message = await message.reply_text(
        f"🔄 **جاري إرسال الإذاعة إلى {len(groups)} مجموعة...**"
    )

    # متغيرات لتتبع التقدم
    success = 0
    failed = 0

    # إرسال الإذاعة
    for group_id in groups:
        try:
            if message.reply_to_message:
                await broadcast_message.copy(group_id)
            else:
                await client.send_message(
                    chat_id=group_id,
                    text=broadcast_text
                )
            success += 1
        except Exception:
            failed += 1

        # تحديث رسالة التقدم كل 25 مجموعة
        if (success + failed) % 25 == 0:
            await progress_message.edit_text(
                f"🔄 **جاري إرسال الإذاعة للمجموعات...**\n\n"
                f"✅ **تم:** {success}\n"
                f"❌ **فشل:** {failed}\n"
                f"⏳ **المتبقي:** {len(groups) - (success + failed)}"
            )

        # تأخير لتجنب التقييد
        await asyncio.sleep(0.1)

    # تسجيل الإذاعة
    await log_broadcast(
        client,
        "للمجموعات",
        message.from_user.id,
        success,
        failed
    )

    # إرسال رسالة الانتهاء
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة للمجموعات بنجاح!**\n\n"
        f"✅ **تم:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"👥 **إجمالي:** {len(groups)}"
    )
