from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from config import START_TEXT, OWNER_ID
from helpers.keyboards import start_keyboard
from helpers.decorators import check_banned, check_force_sub
from database.users_db import add_user
from database.settings_db import get_bot_name, get_source_name
from utils.logger import log_new_user
from helpers.command_handler import command_handler

# معالجة أمر البداية
@Client.on_message(command_handler("start") & filters.private)
@check_banned
@check_force_sub
async def start_command(client: Client, message: Message):
    """معالجة أمر البداية"""
    # إضافة المستخدم إلى قاعدة البيانات
    user_id = message.from_user.id
    username = message.from_user.username
    first_name = message.from_user.first_name
    
    await add_user(user_id, username, first_name)
    
    # تسجيل مستخدم جديد
    await log_new_user(client, user_id, username, first_name)
    
    # الحصول على اسم البوت واسم السورس
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    # إنشاء نص البداية
    start_text = START_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    # إرسال رسالة البداية
    await message.reply_text(
        start_text,
        reply_markup=await start_keyboard(),
        disable_web_page_preview=True
    )

# معالجة أمر السورس
@Client.on_message(command_handler("السورس"))
@check_banned
@check_force_sub
async def source_command(client: Client, message: Message):
    """معالجة أمر السورس"""
    # الحصول على اسم البوت واسم السورس
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    # إنشاء نص السورس
    source_text = f"""
🔰 **{source_name}**

🤖 **اسم البوت:** {bot_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **سورس ميوزك متكامل يدعم تشغيل الصوتيات في المكالمات**
    """
    
    # إنشاء لوحة مفاتيح السورس
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    
    # إرسال رسالة السورس
    await message.reply_text(
        source_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )

# معالجة أمر المطور
@Client.on_message(filters.regex("^مطور السورس$"))
async def dev_command(client: Client, message: Message):
    """معالجة أمر المطور"""
    # الحصول على اسم البوت واسم السورس
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    # إنشاء نص المطور
    dev_text = f"""
👨‍💻 **مطور {source_name}**

🤖 **اسم البوت:** {bot_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **للتواصل مع المطور اضغط على الزر أدناه**
    """
    
    # إنشاء لوحة مفاتيح المطور
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ]
    ]
    
    # إرسال رسالة المطور
    await message.reply_text(
        dev_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )

# معالجة ردود الأزرار
@Client.on_callback_query(filters.regex("^back_to_start$"))
async def back_to_start(client, callback_query):
    """معالجة زر الرجوع إلى البداية"""
    # الحصول على اسم البوت واسم السورس
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    # إنشاء نص البداية
    start_text = START_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    # تحديث رسالة البداية
    await callback_query.edit_message_text(
        start_text,
        reply_markup=await start_keyboard(),
        disable_web_page_preview=True
    )

@Client.on_callback_query(filters.regex("^about$"))
async def about_callback(client, callback_query):
    """معالجة زر حول البوت"""
    # الحصول على اسم البوت واسم السورس
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    # إنشاء نص حول البوت
    about_text = f"""
ℹ️ **معلومات عن {bot_name}**

🤖 **اسم البوت:** {bot_name}
🔰 **السورس:** {source_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **بوت تشغيل موسيقى في المكالمات الصوتية**
🎵 **يدعم تشغيل الموسيقى من يوتيوب والملفات الصوتية**
    """
    
    # إنشاء لوحة مفاتيح حول البوت
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    
    # تحديث رسالة حول البوت
    await callback_query.edit_message_text(
        about_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )
