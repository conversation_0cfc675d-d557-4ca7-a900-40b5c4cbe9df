from pyrogram import Client, filters
from pyrogram.types import Message, CallbackQuery
from config import OWNER_ID
from helpers.decorators import owner_only, dev_only
from helpers.filters import owner, dev
from helpers.command_handler import command_handler
from helpers.keyboards import bot_settings_keyboard
from database.settings_db import (
    set_bot_name, set_source_name, set_developer_name,
    set_channel, set_group, enable_force_sub, disable_force_sub,
    is_force_sub_enabled, enable_advanced_perms, disable_advanced_perms,
    is_advanced_perms_enabled, enable_communication, disable_communication,
    is_communication_enabled
)
from utils.logger import log_settings_change

# معالجة أمر إعدادات البوت
@Client.on_message(command_handler(["settings", "اعدادات"]) & dev)
@dev_only
async def settings_command(client: Client, message: Message):
    """معالجة أمر إعدادات البوت"""
    await message.reply_text(
        "⚙️ **إعدادات البوت**\n\n"
        "اختر الإعداد الذي تريد تغييره من الأزرار أدناه.",
        reply_markup=await bot_settings_keyboard()
    )

# معالجة أمر تعيين اسم البوت
@Client.on_message(command_handler(["setbotname", "تعيين اسم البوت"]) & dev)
@dev_only
async def set_bot_name_command(client: Client, message: Message):
    """معالجة أمر تعيين اسم البوت"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى إدخال اسم البوت الجديد.**\n\n"
            "مثال: `تعيين اسم البوت ميوزك بوت`"
        )
        return

    # الحصول على اسم البوت الجديد
    bot_name = " ".join(message.command[1:])

    # تعيين اسم البوت الجديد
    await set_bot_name(bot_name)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "اسم البوت",
        bot_name,
        message.from_user.id
    )

    await message.reply_text(
        f"✅ **تم تعيين اسم البوت إلى:** `{bot_name}`"
    )

# معالجة أمر تعيين اسم السورس
@Client.on_message(command_handler(["setsourcename", "تعيين اسم السورس"]) & dev)
@dev_only
async def set_source_name_command(client: Client, message: Message):
    """معالجة أمر تعيين اسم السورس"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى إدخال اسم السورس الجديد.**\n\n"
            "مثال: `تعيين اسم السورس سورس ميوزك`"
        )
        return

    # الحصول على اسم السورس الجديد
    source_name = " ".join(message.command[1:])

    # تعيين اسم السورس الجديد
    await set_source_name(source_name)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "اسم السورس",
        source_name,
        message.from_user.id
    )

    await message.reply_text(
        f"✅ **تم تعيين اسم السورس إلى:** `{source_name}`"
    )

# معالجة أمر تعيين اسم المطور
@Client.on_message(command_handler(["setdevname", "تعيين اسم المطور"]) & dev)
@dev_only
async def set_developer_name_command(client: Client, message: Message):
    """معالجة أمر تعيين اسم المطور"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى إدخال اسم المطور الجديد.**\n\n"
            "مثال: `تعيين اسم المطور المطور`"
        )
        return

    # الحصول على اسم المطور الجديد
    developer_name = " ".join(message.command[1:])

    # تعيين اسم المطور الجديد
    await set_developer_name(developer_name)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "اسم المطور",
        developer_name,
        message.from_user.id
    )

    await message.reply_text(
        f"✅ **تم تعيين اسم المطور إلى:** `{developer_name}`"
    )

# معالجة أمر تعيين قناة البوت
@Client.on_message(command_handler(["setchannel", "تعيين قناة البوت"]) & dev)
@dev_only
async def set_channel_command(client: Client, message: Message):
    """معالجة أمر تعيين قناة البوت"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى إدخال رابط القناة الجديد.**\n\n"
            "مثال: `تعيين قناة البوت https://t.me/channel`"
        )
        return

    # الحصول على رابط القناة الجديد
    channel = message.command[1]

    # تعيين رابط القناة الجديد
    await set_channel(channel)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "قناة البوت",
        channel,
        message.from_user.id
    )

    await message.reply_text(
        f"✅ **تم تعيين قناة البوت إلى:** `{channel}`"
    )

# معالجة أمر تعيين مجموعة البوت
@Client.on_message(command_handler(["setgroup", "تعيين مجموعة البوت"]) & dev)
@dev_only
async def set_group_command(client: Client, message: Message):
    """معالجة أمر تعيين مجموعة البوت"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى إدخال رابط المجموعة الجديد.**\n\n"
            "مثال: `تعيين مجموعة البوت https://t.me/group`"
        )
        return

    # الحصول على رابط المجموعة الجديد
    group = message.command[1]

    # تعيين رابط المجموعة الجديد
    await set_group(group)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "مجموعة البوت",
        group,
        message.from_user.id
    )

    await message.reply_text(
        f"✅ **تم تعيين مجموعة البوت إلى:** `{group}`"
    )

# معالجة أمر تفعيلتعطيل الاشتراك الإجباري
@Client.on_message(command_handler(["toggleforcesub", "الاشتراك الاجباري"]) & dev)
@dev_only
async def toggle_force_sub_command(client: Client, message: Message):
    """معالجة أمر تفعيلتعطيل الاشتراك الإجباري"""
    # التحقق من حالة الاشتراك الإجباري
    if await is_force_sub_enabled():
        # تعطيل الاشتراك الإجباري
        await disable_force_sub()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "الاشتراك الإجباري",
            "معطل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تعطيل الاشتراك الإجباري.**"
        )
    else:
        # تفعيل الاشتراك الإجباري
        await enable_force_sub()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "الاشتراك الإجباري",
            "مفعل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تفعيل الاشتراك الإجباري.**"
        )

# معالجة أمر تفعيلتعطيل صلاحيات التشغيل المتقدمة
@Client.on_message(command_handler(["toggleadvancedperms", "صلاحيات التشغيل"]) & dev)
@dev_only
async def toggle_advanced_perms_command(client: Client, message: Message):
    """معالجة أمر تفعيل/تعطيل صلاحيات التشغيل المتقدمة"""
    # التحقق من حالة صلاحيات التشغيل المتقدمة
    if await is_advanced_perms_enabled():
        # تعطيل صلاحيات التشغيل المتقدمة
        await disable_advanced_perms()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "صلاحيات التشغيل المتقدمة",
            "معطل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تعطيل صلاحيات التشغيل المتقدمة.**"
        )
    else:
        # تفعيل صلاحيات التشغيل المتقدمة
        await enable_advanced_perms()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "صلاحيات التشغيل المتقدمة",
            "مفعل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تفعيل صلاحيات التشغيل المتقدمة.**"
        )

# معالجة أمر تفعيل/تعطيل التواصل
@Client.on_message(command_handler(["togglecommunication", "التواصل"]) & dev)
@dev_only
async def toggle_communication_command(client: Client, message: Message):
    """معالجة أمر تفعيل/تعطيل التواصل"""
    # التحقق من حالة التواصل
    if await is_communication_enabled():
        # تعطيل التواصل
        await disable_communication()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "التواصل",
            "معطل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تعطيل التواصل.**"
        )
    else:
        # تفعيل التواصل
        await enable_communication()

        # تسجيل تغيير الإعدادات
        await log_settings_change(
            client,
            "التواصل",
            "مفعل",
            message.from_user.id
        )

        await message.reply_text(
            "✅ **تم تفعيل التواصل.**"
        )

# معالجة ردود الأزرار
@Client.on_callback_query(filters.regex("^set_bot_name$"))
async def set_bot_name_callback(client, callback_query):
    """معالجة زر تعيين اسم البوت"""
    # التحقق من صلاحية المستخدم
    if not (callback_query.from_user.id == OWNER_ID or await dev.func(client, callback_query)):
        await callback_query.answer("⚠️ هذا الأمر متاح فقط للمطورين.", show_alert=True)
        return

    # طلب اسم البوت الجديد
    await callback_query.edit_message_text(
        "✏️ **تعيين اسم البوت**\n\n"
        "يرجى إرسال اسم البوت الجديد."
    )

    # انتظار رد المستخدم
    response = await client.listen(callback_query.from_user.id)

    # تعيين اسم البوت الجديد
    await set_bot_name(response.text)

    # تسجيل تغيير الإعدادات
    await log_settings_change(
        client,
        "اسم البوت",
        response.text,
        callback_query.from_user.id
    )

    # إرسال رسالة التأكيد
    await response.reply_text(
        f"✅ **تم تعيين اسم البوت إلى:** `{response.text}`",
        reply_markup=await bot_settings_keyboard()
    )

# معالجة باقي ردود الأزرار بنفس الطريقة
# (تم اختصار الكود هنا لتجنب التكرار)
