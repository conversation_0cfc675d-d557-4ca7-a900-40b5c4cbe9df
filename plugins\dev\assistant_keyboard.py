from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from helpers.keyboards import assistant_keyboard
from database.settings_db import get_all_assistant_accounts

# معالجة أمر إدارة المساعد بالكيبورد
@Client.on_message(command_handler(["assistant keyboard", "كيبورد المساعد", "لوحة المساعد"]) & dev)
@dev_only
async def assistant_keyboard_command(client: Client, message: Message):
    """معالجة أمر إدارة المساعد بالكيبورد"""
    
    # الحصول على معلومات المساعد
    assistants = await get_all_assistant_accounts()
    
    if assistants:
        user_id = list(assistants.keys())[0]
        account = assistants[user_id]
        
        # التحقق من حالة الاتصال
        status = "✅ متصل" if hasattr(client, 'assistant') and client.assistant else "❌ غير متصل"
        
        text = (
            f"👤 **إدارة الحساب المساعد**\n\n"
            f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
            f"**المعرف:** `{user_id}`\n"
            f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n"
            f"**الحالة:** {status}\n\n"
            f"🎛 **استخدم الأزرار أدناه لإدارة الحساب المساعد:**"
        )
    else:
        text = (
            f"👤 **إدارة الحساب المساعد**\n\n"
            f"⚠️ **لا يوجد حساب مساعد مضاف حالياً.**\n\n"
            f"يمكنك إضافة حساب مساعد جديد باستخدام الزر أدناه.\n\n"
            f"🎛 **استخدم الأزرار أدناه للبدء:**"
        )

    await message.reply_text(
        text,
        reply_markup=await assistant_keyboard()
    )

# معالجة أمر إدارة المساعد المختصر
@Client.on_message(command_handler(["امساعد", "مساعد كيبورد"]) & dev)
@dev_only
async def assistant_keyboard_short_command(client: Client, message: Message):
    """معالجة أمر إدارة المساعد المختصر"""
    await assistant_keyboard_command(client, message)
