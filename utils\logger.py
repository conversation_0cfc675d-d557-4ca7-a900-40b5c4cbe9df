import logging
from pyrogram import Client
from database.settings_db import is_log_enabled, get_log_channel

# إعداد التسجيل
LOGGER = logging.getLogger(__name__)

# إرسال سجل إلى قناة التسجيل
async def send_log(client: Client, message: str, disable_notification: bool = False):
    """إرسال سجل إلى قناة التسجيل"""
    # التحقق مما إذا كان التسجيل مفعلاً
    if not await is_log_enabled():
        return False
    
    # الحصول على معرف قناة التسجيل
    log_channel = await get_log_channel()
    if not log_channel:
        return False
    
    try:
        # إرسال الرسالة إلى قناة التسجيل
        await client.send_message(
            chat_id=log_channel,
            text=message,
            disable_notification=disable_notification,
            disable_web_page_preview=True
        )
        return True
    except Exception as e:
        LOGGER.error(f"فشل إرسال السجل: {e}")
        return False

# تسجيل تشغيل أغنية
async def log_stream(client: Client, chat_id: int, chat_title: str, user_id: int, user_name: str, song_name: str, duration: str = None):
    """تسجيل تشغيل أغنية"""
    message = (
        f"🎵 **تم تشغيل أغنية جديدة**\n\n"
        f"**المجموعة:** {chat_title} [`{chat_id}`]\n"
        f"**بواسطة:** {user_name} [`{user_id}`]\n"
        f"**الأغنية:** `{song_name}`\n"
    )
    
    if duration:
        message += f"**المدة:** `{duration}`\n"
    
    await send_log(client, message)

# تسجيل انضمام البوت إلى مجموعة جديدة
async def log_new_group(client: Client, chat_id: int, chat_title: str, chat_username: str, added_by: int):
    """تسجيل انضمام البوت إلى مجموعة جديدة"""
    chat_link = f"https://t.me/{chat_username}" if chat_username else "Private Group"
    
    message = (
        f"🔰 **تم تفعيل محادثة جديدة تلقائياً**\n\n"
        f"**المجموعة:** [{chat_title}]({chat_link})\n"
        f"**معرف المجموعة:** `{chat_id}`\n"
        f"**بواسطة:** `{added_by}`\n"
    )
    
    await send_log(client, message)

# تسجيل مستخدم جديد
async def log_new_user(client: Client, user_id: int, username: str, first_name: str):
    """تسجيل مستخدم جديد"""
    user_link = f"https://t.me/{username}" if username else f"tg://user?id={user_id}"
    
    message = (
        f"👤 **مستخدم جديد**\n\n"
        f"**الاسم:** [{first_name}]({user_link})\n"
        f"**المعرف:** `{user_id}`\n"
    )
    
    await send_log(client, message, disable_notification=True)

# تسجيل إذاعة
async def log_broadcast(client: Client, broadcast_type: str, user_id: int, success: int, failed: int):
    """تسجيل إذاعة"""
    message = (
        f"📢 **تم إرسال إذاعة**\n\n"
        f"**النوع:** `{broadcast_type}`\n"
        f"**بواسطة:** `{user_id}`\n"
        f"**ناجح:** `{success}`\n"
        f"**فاشل:** `{failed}`\n"
    )
    
    await send_log(client, message)

# تسجيل حظر مستخدم
async def log_ban(client: Client, user_id: int, banned_by: int, reason: str = None):
    """تسجيل حظر مستخدم"""
    message = (
        f"🚫 **تم حظر مستخدم**\n\n"
        f"**المستخدم:** `{user_id}`\n"
        f"**بواسطة:** `{banned_by}`\n"
    )
    
    if reason:
        message += f"**السبب:** `{reason}`\n"
    
    await send_log(client, message)

# تسجيل إلغاء حظر مستخدم
async def log_unban(client: Client, user_id: int, unbanned_by: int):
    """تسجيل إلغاء حظر مستخدم"""
    message = (
        f"✅ **تم إلغاء حظر مستخدم**\n\n"
        f"**المستخدم:** `{user_id}`\n"
        f"**بواسطة:** `{unbanned_by}`\n"
    )
    
    await send_log(client, message)

# تسجيل تغيير إعدادات البوت
async def log_settings_change(client: Client, setting_name: str, new_value: str, changed_by: int):
    """تسجيل تغيير إعدادات البوت"""
    message = (
        f"⚙️ **تم تغيير إعدادات البوت**\n\n"
        f"**الإعداد:** `{setting_name}`\n"
        f"**القيمة الجديدة:** `{new_value}`\n"
        f"**بواسطة:** `{changed_by}`\n"
    )
    
    await send_log(client, message)
