import os
import asyncio
from pyrogram import Client, filters
from pyrogram.types import Message
from pyrogram.errors import UserAlreadyParticipant, InviteHashExpired, UsernameNotOccupied
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.settings_db import (
    add_assistant_account, remove_assistant_account,
    get_assistant_account, get_all_assistant_accounts,
    update_assistant_account
)
from config import API_ID, API_HASH

# معالجة أمر إضافة حساب مساعد
@Client.on_message(command_handler(["addassistant", "اضافة حساب مساعد"]) & dev)
@dev_only
async def add_assistant_command(client: Client, message: Message):
    """معالجة أمر إضافة حساب مساعد"""
    # التحقق من وجود حساب مساعد
    assistants = await get_all_assistant_accounts()
    if assistants:
        await message.reply_text(
            "⚠️ **يوجد حساب مساعد بالفعل!**\n\n"
            "يمكنك استخدام أمر `حذف الحساب مساعد` لحذف الحساب الحالي أولاً."
        )
        return
    
    # إرسال رسالة طلب رقم الهاتف
    await message.reply_text(
        "📱 **يرجى إرسال رقم الهاتف للحساب المساعد.**\n\n"
        "مثال: `+**********`"
    )
    
    # انتظار رد المستخدم برقم الهاتف
    phone_message = await client.listen(message.chat.id, filters=filters.text)
    phone_number = phone_message.text.strip()
    
    # إرسال رسالة البدء
    status_message = await message.reply_text(
        f"🔄 **جاري إضافة الحساب المساعد...**\n\n"
        f"📱 **رقم الهاتف:** `{phone_number}`\n\n"
        f"⏳ **يرجى الانتظار...**"
    )
    
    try:
        # إنشاء جلسة جديدة
        assistant = Client(
            f"assistant_{phone_number.replace('+', '')}",
            api_id=API_ID,
            api_hash=API_HASH,
            phone_number=phone_number,
            in_memory=False
        )
        
        # بدء الجلسة
        await assistant.connect()
        
        # طلب رمز التحقق
        sent_code = await assistant.send_code(phone_number)
        
        # طلب رمز التحقق من المستخدم
        await status_message.edit_text(
            f"📱 **تم إرسال رمز التحقق إلى الرقم {phone_number}**\n\n"
            f"🔢 **يرجى إرسال رمز التحقق.**"
        )
        
        # انتظار رد المستخدم
        code_message = await client.listen(message.chat.id, filters=filters.text)
        code = code_message.text.strip()
        
        # تسجيل الدخول باستخدام رمز التحقق
        await status_message.edit_text(
            f"🔄 **جاري تسجيل الدخول باستخدام رمز التحقق...**"
        )
        
        try:
            await assistant.sign_in(phone_number, sent_code.phone_code_hash, code)
        except Exception as e:
            # التحقق مما إذا كان هناك حاجة إلى كلمة مرور
            if "PASSWORD_HASH_INVALID" in str(e):
                await message.reply_text(
                    f"🔐 **يرجى إرسال كلمة المرور (التحقق بخطوتين).**"
                )
                
                # انتظار رد المستخدم
                password_message = await client.listen(message.chat.id, filters=filters.text)
                password = password_message.text.strip()
                
                # تسجيل الدخول باستخدام كلمة المرور
                await status_message.edit_text(
                    f"🔄 **جاري تسجيل الدخول باستخدام كلمة المرور...**"
                )
                
                await assistant.check_password(password)
            else:
                raise e
        
        # الحصول على معلومات الحساب المساعد
        me = await assistant.get_me()
        
        # الحصول على سلسلة الجلسة
        session_string = await assistant.export_session_string()
        
        # إضافة الحساب المساعد إلى قاعدة البيانات
        await add_assistant_account(
            me.id,
            session_string,
            me.first_name,
            me.username
        )
        
        # إيقاف الجلسة
        await assistant.disconnect()
        
        # إرسال رسالة النجاح
        await status_message.edit_text(
            f"✅ **تم إضافة الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}\n\n"
            f"🔄 **جاري إعادة تشغيل البوت لتفعيل الحساب المساعد...**"
        )
        
        # إعادة تشغيل البوت
        import sys
        import os
        os.execv(sys.executable, ['python'] + sys.argv)
    
    except Exception as e:
        # إرسال رسالة الخطأ
        await status_message.edit_text(
            f"❌ **حدث خطأ أثناء إضافة الحساب المساعد:**\n\n"
            f"`{str(e)}`"
        )

# معالجة أمر حذف حساب مساعد
@Client.on_message(command_handler(["removeassistant", "حذف الحساب مساعد"]) & dev)
@dev_only
async def remove_assistant_command(client: Client, message: Message):
    """معالجة أمر حذف حساب مساعد"""
    # الحصول على الحساب المساعد
    assistants = await get_all_assistant_accounts()
    
    if not assistants:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد لحذفه.**"
        )
        return
    
    # الحصول على معرف الحساب المساعد
    user_id = list(assistants.keys())[0]
    account = assistants[user_id]
    
    # حذف الحساب المساعد من قاعدة البيانات
    if await remove_assistant_account(user_id):
        await message.reply_text(
            f"✅ **تم حذف الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {account.get('first_name', 'غير معروف')}\n"
            f"🆔 **المعرف:** `{user_id}`\n"
            f"📝 **اسم المستخدم:** @{account.get('username', 'لا يوجد')}"
        )
    else:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء حذف الحساب المساعد.**"
        )

# معالجة أمر عرض الحساب المساعد
@Client.on_message(command_handler(["assistant", "الحساب المساعد"]) & dev)
@dev_only
async def list_assistants_command(client: Client, message: Message):
    """معالجة أمر عرض الحساب المساعد"""
    # الحصول على الحساب المساعد
    assistants = await get_all_assistant_accounts()
    
    if not assistants:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد مضاف.**"
        )
        return
    
    # الحصول على معلومات الحساب المساعد
    user_id = list(assistants.keys())[0]
    account = assistants[user_id]
    
    # التحقق من حالة الحساب المساعد
    status = "✅ متصل" if client.assistant else "❌ غير متصل"
    
    # إنشاء نص معلومات الحساب المساعد
    assistant_text = (
        f"👤 **معلومات الحساب المساعد:**\n\n"
        f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
        f"**المعرف:** `{user_id}`\n"
        f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n"
        f"**الحالة:** {status}"
    )
    
    await message.reply_text(assistant_text)

# معالجة أمر دعوة الحساب المساعد
@Client.on_message(command_handler(["inviteassistant", "دعوة المساعد"]) & dev)
@dev_only
async def invite_assistant_command(client: Client, message: Message):
    """معالجة أمر دعوة الحساب المساعد"""
    if not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**"
        )
        return
    
    try:
        # دعوة الحساب المساعد إلى المجموعة
        invite_link = await client.export_chat_invite_link(message.chat.id)
        
        # استخدام الحساب المساعد للانضمام
        await client.assistant.join_chat(invite_link)
        
        # الحصول على معلومات الحساب المساعد
        me = await client.assistant.get_me()
        
        await message.reply_text(
            f"✅ **تم انضمام الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}"
        )
    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء دعوة الحساب المساعد:**\n\n"
            f"`{str(e)}`"
        )

# معالجة أمر خروج المساعد من المجموعات
@Client.on_message(command_handler(["leavegroups", "خروج المساعد"]) & dev)
@dev_only
async def leave_groups_command(client: Client, message: Message):
    """معالجة أمر خروج المساعد من المجموعات"""
    if not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**"
        )
        return
    
    try:
        # خروج الحساب المساعد من المجموعة
        await client.assistant.leave_chat(message.chat.id)
        
        # الحصول على معلومات الحساب المساعد
        me = await client.assistant.get_me()
        
        await message.reply_text(
            f"✅ **تم خروج الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}"
        )
    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء خروج الحساب المساعد:**\n\n"
            f"`{str(e)}`"
        )
