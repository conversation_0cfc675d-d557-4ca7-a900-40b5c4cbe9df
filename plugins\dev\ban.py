from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import (
    ban_user, unban_user, is_user_banned,
    ban_group, unban_group, is_group_banned,
    get_all_banned_users, get_all_banned_groups
)
from utils.extraction import extract_user, extract_group
from utils.logger import log_ban, log_unban

# معالجة أمر حظر مستخدم
@Client.on_message(command_handler(["ban", "حظر"]) & dev)
@dev_only
async def ban_user_command(client: Client, message: Message):
    """معالجة أمر حظر مستخدم"""
    # استخراج معرف المستخدم
    user_id = await extract_user(message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد حظره.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "- الرد على رسالة المستخدم\n"
            "- كتابة معرف المستخدم\n"
            "- كتابة اسم مستخدم المستخدم\n\n"
            "مثال: `حظر @username`"
        )
        return

    # التحقق مما إذا كان المستخدم محظورًا بالفعل
    if await is_user_banned(user_id):
        await message.reply_text(
            f"⚠️ **المستخدم {user_id} محظور بالفعل.**"
        )
        return

    # حظر المستخدم
    await ban_user(user_id)

    # تسجيل الحظر
    reason = " ".join(message.command[2:]) if len(message.command) > 2 else None
    await log_ban(client, user_id, message.from_user.id, reason)

    # إرسال رسالة التأكيد
    ban_text = f"✅ **تم حظر المستخدم {user_id} بنجاح.**"
    if reason:
        ban_text += f"\n**السبب:** {reason}"

    await message.reply_text(ban_text)

# معالجة أمر إلغاء حظر مستخدم
@Client.on_message(command_handler(["unban", "الغاء حظر"]) & dev)
@dev_only
async def unban_user_command(client: Client, message: Message):
    """معالجة أمر إلغاء حظر مستخدم"""
    # استخراج معرف المستخدم
    user_id = await extract_user(message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد إلغاء حظره.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "- الرد على رسالة المستخدم\n"
            "- كتابة معرف المستخدم\n"
            "- كتابة اسم مستخدم المستخدم\n\n"
            "مثال: `الغاء حظر @username`"
        )
        return

    # التحقق مما إذا كان المستخدم محظورًا
    if not await is_user_banned(user_id):
        await message.reply_text(
            f"⚠️ **المستخدم {user_id} غير محظور.**"
        )
        return

    # إلغاء حظر المستخدم
    await unban_user(user_id)

    # تسجيل إلغاء الحظر
    await log_unban(client, user_id, message.from_user.id)

    # إرسال رسالة التأكيد
    await message.reply_text(
        f"✅ **تم إلغاء حظر المستخدم {user_id} بنجاح.**"
    )

# معالجة أمر حظر مجموعة
@Client.on_message(command_handler(["bangroup", "حظر مجموعة"]) & dev)
@dev_only
async def ban_group_command(client: Client, message: Message):
    """معالجة أمر حظر مجموعة"""
    # استخراج معرف المجموعة
    group_id = await extract_group(message)

    if not group_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المجموعة المراد حظرها.**\n\n"
            "يمكنك تحديد المجموعة بإحدى الطرق التالية:\n"
            "- كتابة معرف المجموعة\n"
            "- كتابة اسم مستخدم المجموعة\n\n"
            "مثال: `حظر مجموعة @groupname`"
        )
        return

    # التحقق مما إذا كانت المجموعة محظورة بالفعل
    if await is_group_banned(group_id):
        await message.reply_text(
            f"⚠️ **المجموعة {group_id} محظورة بالفعل.**"
        )
        return

    # حظر المجموعة
    await ban_group(group_id)

    # إرسال رسالة التأكيد
    await message.reply_text(
        f"✅ **تم حظر المجموعة {group_id} بنجاح.**"
    )

# معالجة أمر إلغاء حظر مجموعة
@Client.on_message(command_handler(["unbangroup", "الغاء حظر مجموعة"]) & dev)
@dev_only
async def unban_group_command(client: Client, message: Message):
    """معالجة أمر إلغاء حظر مجموعة"""
    # استخراج معرف المجموعة
    group_id = await extract_group(message)

    if not group_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المجموعة المراد إلغاء حظرها.**\n\n"
            "يمكنك تحديد المجموعة بإحدى الطرق التالية:\n"
            "- كتابة معرف المجموعة\n"
            "- كتابة اسم مستخدم المجموعة\n\n"
            "مثال: `الغاء حظر مجموعة @groupname`"
        )
        return

    # التحقق مما إذا كانت المجموعة محظورة
    if not await is_group_banned(group_id):
        await message.reply_text(
            f"⚠️ **المجموعة {group_id} غير محظورة.**"
        )
        return

    # إلغاء حظر المجموعة
    await unban_group(group_id)

    # إرسال رسالة التأكيد
    await message.reply_text(
        f"✅ **تم إلغاء حظر المجموعة {group_id} بنجاح.**"
    )

# معالجة أمر قائمة المستخدمين المحظورين
@Client.on_message(command_handler(["banlist", "قائمة المحظورين"]) & dev)
@dev_only
async def ban_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المستخدمين المحظورين"""
    # الحصول على قائمة المستخدمين المحظورين
    banned_users = await get_all_banned_users()

    if not banned_users:
        await message.reply_text(
            "⚠️ **لا يوجد مستخدمين محظورين.**"
        )
        return

    # إنشاء نص قائمة المستخدمين المحظورين
    ban_text = f"🚫 **قائمة المستخدمين المحظورين ({len(banned_users)}):**\n\n"

    for i, user_id in enumerate(banned_users, 1):
        ban_text += f"{i}. `{user_id}`\n"

    await message.reply_text(ban_text)

# معالجة أمر قائمة المجموعات المحظورة
@Client.on_message(command_handler(["bangrouplist", "قائمة المجموعات المحظورة"]) & dev)
@dev_only
async def ban_group_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المجموعات المحظورة"""
    # الحصول على قائمة المجموعات المحظورة
    banned_groups = await get_all_banned_groups()

    if not banned_groups:
        await message.reply_text(
            "⚠️ **لا يوجد مجموعات محظورة.**"
        )
        return

    # إنشاء نص قائمة المجموعات المحظورة
    ban_text = f"🚫 **قائمة المجموعات المحظورة ({len(banned_groups)}):**\n\n"

    for i, group_id in enumerate(banned_groups, 1):
        ban_text += f"{i}. `{group_id}`\n"

    await message.reply_text(ban_text)
