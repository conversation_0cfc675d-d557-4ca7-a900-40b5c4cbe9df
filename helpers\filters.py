from pyrogram import filters
from config import OWNER_ID
from database.users_db import is_developer, is_admin, is_vip

# فلتر مطور السورس
async def owner_filter(_, __, message):
    """فلتر للتحقق من مطور السورس"""
    return message.from_user and message.from_user.id == OWNER_ID

# فلتر المطورين
async def dev_filter(_, __, message):
    """فلتر للتحقق من المطورين"""
    if not message.from_user:
        return False
    
    user_id = message.from_user.id
    return user_id == OWNER_ID or await is_developer(user_id)

# فلتر المشرفين
async def admin_filter(_, client, message):
    """فلتر للتحقق من المشرفين"""
    if not message.from_user:
        return False
    
    user_id = message.from_user.id
    
    # التحقق من المطور والمطورين والمشرفين
    if (user_id == OWNER_ID or 
        await is_developer(user_id) or 
        await is_admin(user_id)):
        return True
    
    # التحقق من مشرفي المجموعة
    if message.chat.type in ["group", "supergroup"]:
        member = await client.get_chat_member(
            message.chat.id, user_id
        )
        return member.status in ["creator", "administrator"]
    
    return False

# فلتر المستخدمين المميزين
async def vip_filter(_, __, message):
    """فلتر للتحقق من المستخدمين المميزين"""
    if not message.from_user:
        return False
    
    user_id = message.from_user.id
    return (user_id == OWNER_ID or 
            await is_developer(user_id) or 
            await is_admin(user_id) or 
            await is_vip(user_id))

# إنشاء الفلاتر
owner = filters.create(owner_filter)
dev = filters.create(dev_filter)
admin = filters.create(admin_filter)
vip = filters.create(vip_filter)
