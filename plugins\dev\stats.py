from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import count_users, count_groups, get_all_users, get_all_groups

# معالجة أمر عدد المستخدمين
@Client.on_message(command_handler(["users", "عدد المستخدمين"]) & dev)
@dev_only
async def users_count_command(client: Client, message: Message):
    """معالجة أمر عدد المستخدمين"""
    # الحصول على عدد المستخدمين
    users_count = await count_users()

    await message.reply_text(
        f"👥 **عدد المستخدمين:** `{users_count}`"
    )

# معالجة أمر عدد المجموعات
@Client.on_message(command_handler(["groups", "عدد المجموعات"]) & dev)
@dev_only
async def groups_count_command(client: Client, message: Message):
    """معالجة أمر عدد المجموعات"""
    # الحصول على عدد المجموعات
    groups_count = await count_groups()

    await message.reply_text(
        f"👥 **عدد المجموعات:** `{groups_count}`"
    )

# معالجة أمر الإحصائيات
@Client.on_message(command_handler(["stats", "الاحصائيات"]) & dev)
@dev_only
async def stats_command(client: Client, message: Message):
    """معالجة أمر الإحصائيات"""
    # الحصول على عدد المستخدمين والمجموعات
    users_count = await count_users()
    groups_count = await count_groups()

    # الحصول على معلومات البوت
    bot_info = await client.get_me()

    stats_text = (
        f"📊 **إحصائيات {bot_info.first_name}**\n\n"
        f"👤 **عدد المستخدمين:** `{users_count}`\n"
        f"👥 **عدد المجموعات:** `{groups_count}`\n"
        f"🤖 **معرف البوت:** `{bot_info.id}`\n"
        f"⚡ **اسم المستخدم:** @{bot_info.username}\n"
    )

    await message.reply_text(stats_text)

# معالجة أمر قائمة المستخدمين
@Client.on_message(command_handler(["userlist", "قائمة المستخدمين"]) & dev)
@dev_only
async def user_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المستخدمين"""
    # الحصول على قائمة المستخدمين
    users = await get_all_users()

    if not users:
        await message.reply_text("⚠️ **لا يوجد مستخدمين.**")
        return

    # إنشاء نص قائمة المستخدمين
    users_text = "👥 **قائمة المستخدمين:**\n\n"

    for i, user_id in enumerate(users[:50], 1):
        users_text += f"{i}. `{user_id}`\n"

    if len(users) > 50:
        users_text += f"\n⚠️ **تم عرض 50 مستخدم فقط من أصل {len(users)}.**"

    await message.reply_text(users_text)

# معالجة أمر قائمة المجموعات
@Client.on_message(command_handler(["grouplist", "قائمة المجموعات"]) & dev)
@dev_only
async def group_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المجموعات"""
    # الحصول على قائمة المجموعات
    groups = await get_all_groups()

    if not groups:
        await message.reply_text("⚠️ **لا يوجد مجموعات.**")
        return

    # إنشاء نص قائمة المجموعات
    groups_text = "👥 **قائمة المجموعات:**\n\n"

    for i, group_id in enumerate(groups[:50], 1):
        try:
            chat = await client.get_chat(group_id)
            group_name = chat.title
            groups_text += f"{i}. `{group_id}` - {group_name}\n"
        except Exception:
            groups_text += f"{i}. `{group_id}` - غير معروف\n"

    if len(groups) > 50:
        groups_text += f"\n⚠️ **تم عرض 50 مجموعة فقط من أصل {len(groups)}.**"

    await message.reply_text(groups_text)
